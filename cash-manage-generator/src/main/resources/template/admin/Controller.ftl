/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package ${package}.rest;

import com.jinghang.cash.modules.manage.RestResult;
import ${package}.domain.${className};
import ${package}.service.${className}Service;
import ${package}.domain.dto.${className}QueryCriteria;
import com.jinghang.cash.utils.PageResult;
import lombok.RequiredArgsConstructor;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date ${date}
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "${apiAlias}")
@RequestMapping("/api/${changeClassName}")
public class ${className}Controller {

    private final ${className}Service ${changeClassName}Service;

    @GetMapping("/queryPage")
    @ApiOperation("查询${apiAlias}")
    @PreAuthorize("@el.check('${changeClassName}:list')")
    public RestResult<PageResult<${className}>> query${className}(${className}QueryCriteria criteria){
        return RestResult.success(${changeClassName}Service.queryAllPage(criteria));
    }

    @PostMapping("/create")
    @ApiOperation("新增${apiAlias}")
    @PreAuthorize("@el.check('${changeClassName}:add')")
    public RestResult<Object> create${className}(@Validated @RequestBody ${className} resources){
        ${changeClassName}Service.create(resources);
        return RestResult.success(HttpStatus.CREATED);
    }

    @PutMapping("/update")
    @ApiOperation("修改${apiAlias}")
    @PreAuthorize("@el.check('${changeClassName}:edit')")
    public RestResult<Object> update${className}(@Validated @RequestBody ${className} resources){
        ${changeClassName}Service.update(resources);
        return RestResult.success(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping("/del")
    @ApiOperation("删除${apiAlias}")
    @PreAuthorize("@el.check('${changeClassName}:del')")
    public RestResult<Object> delete${className}(@ApiParam(value = "传ID数组[]") @RequestBody List<${pkColumnType}> ids) {
        ${changeClassName}Service.deleteAll(ids);
        return RestResult.success(HttpStatus.OK);
    }

    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('${changeClassName}:list')")
    public void export${className}(HttpServletResponse response, ${className}QueryCriteria criteria) throws IOException {
    ${changeClassName}Service.download(${changeClassName}Service.queryAll(criteria), response);
    }
}